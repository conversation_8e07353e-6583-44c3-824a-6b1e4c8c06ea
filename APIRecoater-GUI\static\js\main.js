document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const connectionStatus = document.getElementById('connection-status');
    const stateValue = document.getElementById('state-value');
    const xPosValue = document.getElementById('x-pos-value');
    const xHomedValue = document.getElementById('x-homed-value');
    const zPosValue = document.getElementById('z-pos-value');
    const zHomedValue = document.getElementById('z-homed-value');
    const gripperStateValue = document.getElementById('gripper-state-value');
    const zOffsetValue = document.getElementById('z-offset-value');
    const logPanel = document.getElementById('log-panel');

    const btnEstop = document.getElementById('btn-estop');
    const btnHomeX = document.getElementById('btn-home-x');
    const btnHomeZ = document.getElementById('btn-home-z');

    // Phase 2 Manual Control Elements
    const xPositionInput = document.getElementById('x-position');
    const xSpeedInput = document.getElementById('x-speed');
    const btnMoveX = document.getElementById('btn-move-x');

    const zPositionInput = document.getElementById('z-position');
    const zSpeedInput = document.getElementById('z-speed');
    const btnMoveZ = document.getElementById('btn-move-z');
    const btnSetZZero = document.getElementById('btn-set-z-zero');

    const gripperCurrentState = document.getElementById('gripper-current-state');
    const btnOpenGripper = document.getElementById('btn-open-gripper');
    const btnCloseGripper = document.getElementById('btn-close-gripper');

    // Phase 3 Print Setup Elements
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const printProgressValue = document.getElementById('print-progress-value');

    // Build area configuration
    const buildAreaShapeRadios = document.querySelectorAll('input[name="build-area-shape"]');
    const circleConfig = document.getElementById('circle-config');
    const rectangleConfig = document.getElementById('rectangle-config');

    // Geometry upload elements
    const drumFileInputs = [
        document.getElementById('drum-0-file'),
        document.getElementById('drum-1-file'),
        document.getElementById('drum-2-file')
    ];
    const drumUploadButtons = [
        document.getElementById('btn-upload-drum-0'),
        document.getElementById('btn-upload-drum-1'),
        document.getElementById('btn-upload-drum-2')
    ];
    const drumStatusSpans = [
        document.getElementById('drum-0-status'),
        document.getElementById('drum-1-status'),
        document.getElementById('drum-2-status')
    ];

    // Print parameters
    const layerStartInput = document.getElementById('layer-start');
    const layerEndInput = document.getElementById('layer-end');
    const fillingDrumSelect = document.getElementById('filling-drum-id');
    const patterningSpeedInput = document.getElementById('patterning-speed');
    const travelSpeedInput = document.getElementById('travel-speed');
    const xOffsetInput = document.getElementById('x-offset');
    const zOffsetParamInput = document.getElementById('z-offset-param');
    const layerThicknessInput = document.getElementById('layer-thickness');
    const collectorsDelayInput = document.getElementById('collectors-delay');
    const powderSavingCheckbox = document.getElementById('powder-saving');
    const btnSetParameters = document.getElementById('btn-set-parameters');

    // Print control
    const printStatusText = document.getElementById('print-status-text');
    const printProgressText = document.getElementById('print-progress-text');
    const btnStartPrint = document.getElementById('btn-start-print');
    const btnCancelPrint = document.getElementById('btn-cancel-print');

    const POLLING_INTERVAL = 2000; // 2 seconds

    // --- API Functions ---
    async function pollStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();

            if (data.connection === 'ok') {
                updateStatusUI(data);
            } else {
                handleConnectionError(data);
            }
        } catch (error) {
            handleConnectionError({ message: 'Frontend fetch failed. Is the server running?' });
        }
    }

    async function sendCommand(endpoint, data = null) {
        try {
            const options = { method: 'POST' };
            if (data) {
                options.headers = { 'Content-Type': 'application/json' };
                options.body = JSON.stringify(data);
            }

            const response = await fetch(endpoint, options);
            const result = await response.json();

            if (result.success) {
                log(`Command ${endpoint}: Success`);
            } else {
                log(`Command ${endpoint}: Failed - ${result.message || 'Unknown error'}`, 'error');
            }
            return result;
        } catch (error) {
            log(`Command ${endpoint} failed: ${error}`, 'error');
            return { success: false };
        }
    }

    // Phase 2 API Functions
    async function moveXAxis() {
        const position = parseFloat(xPositionInput.value);
        const speed = parseFloat(xSpeedInput.value);

        if (!validateInput(position, 0, 1000, 'X position') ||
            !validateInput(speed, 1, 100, 'X speed')) {
            return;
        }

        return await sendCommand('/api/move_x', { position, speed });
    }

    async function moveZAxis() {
        const position = parseFloat(zPositionInput.value);
        const speed = parseFloat(zSpeedInput.value);

        if (!validateInput(position, -100, 100, 'Z position') ||
            !validateInput(speed, 0.1, 20, 'Z speed')) {
            return;
        }

        return await sendCommand('/api/move_z', { position, speed });
    }

    async function setZZero() {
        const currentZPos = parseFloat(zPosValue.textContent);
        if (isNaN(currentZPos)) {
            log('Cannot set Z zero: Current Z position is unknown', 'error');
            return;
        }

        return await sendCommand('/api/set_z_offset', { offset: currentZPos });
    }

    async function controlGripper(state) {
        return await sendCommand('/api/gripper', { state });
    }

    // Phase 3 API Functions
    async function uploadGeometry(drumId, file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch(`/api/upload_geometry/${drumId}`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                log(`Geometry uploaded to drum ${drumId}: Success`);
                drumStatusSpans[drumId].textContent = 'Upload successful';
                drumStatusSpans[drumId].className = 'upload-status success';
            } else {
                log(`Geometry upload to drum ${drumId}: Failed - ${result.message || 'Unknown error'}`, 'error');
                drumStatusSpans[drumId].textContent = `Upload failed: ${result.message}`;
                drumStatusSpans[drumId].className = 'upload-status error';
            }
            return result;
        } catch (error) {
            log(`Geometry upload to drum ${drumId} failed: ${error}`, 'error');
            drumStatusSpans[drumId].textContent = 'Upload failed';
            drumStatusSpans[drumId].className = 'upload-status error';
            return { success: false };
        }
    }

    async function setPrintParameters() {
        const parameters = {
            layer_start: parseInt(layerStartInput.value),
            layer_end: parseInt(layerEndInput.value),
            filling_drum_id: parseInt(fillingDrumSelect.value),
            patterning_speed: parseFloat(patterningSpeedInput.value),
            travel_speed: parseFloat(travelSpeedInput.value),
            x_offset: parseFloat(xOffsetInput.value),
            z_offset: parseFloat(zOffsetParamInput.value),
            layer_thickness: parseFloat(layerThicknessInput.value),
            collectors_delay: parseInt(collectorsDelayInput.value),
            powder_saving: powderSavingCheckbox.checked
        };

        // Validate parameters
        if (parameters.layer_start < 1 || parameters.layer_end < parameters.layer_start) {
            log('Invalid layer range', 'error');
            return { success: false };
        }

        if (parameters.patterning_speed <= 0 || parameters.travel_speed <= 0) {
            log('Speeds must be positive', 'error');
            return { success: false };
        }

        if (parameters.layer_thickness <= 0) {
            log('Layer thickness must be positive', 'error');
            return { success: false };
        }

        if (parameters.collectors_delay < 0) {
            log('Collectors delay must be non-negative', 'error');
            return { success: false };
        }

        return await sendCommand('/api/print/parameters', parameters);
    }

    async function startPrint() {
        if (confirm('Start print job? Make sure all parameters are set correctly.')) {
            return await sendCommand('/api/print/start');
        }
        return { success: false };
    }

    async function cancelPrint() {
        if (confirm('Cancel current print job? This action cannot be undone.')) {
            return await sendCommand('/api/print/cancel');
        }
        return { success: false };
    }

    // --- UI Update Functions ---
    function updateStatusUI(data) {
        connectionStatus.className = 'status-dot-green';
        stateValue.textContent = data.state;
        xPosValue.textContent = `${data.x.position.toFixed(3)} mm`;
        xHomedValue.textContent = data.x.homed;
        zPosValue.textContent = `${data.z.position.toFixed(3)} mm`;
        zHomedValue.textContent = data.z.homed;

        // Phase 2 updates
        if (data.gripper && data.gripper.state) {
            gripperStateValue.textContent = data.gripper.state;
            gripperCurrentState.textContent = data.gripper.state;
        }
        if (data.z_offset !== undefined) {
            zOffsetValue.textContent = `${data.z_offset.toFixed(3)} mm`;
        }

        // Phase 3 updates
        if (data.print) {
            const currentLayer = data.print.current_layer || 0;
            const totalLayers = data.print.total_layers || 0;
            const isPrinting = data.print.is_printing || false;

            printProgressValue.textContent = `${currentLayer} / ${totalLayers}`;
            printProgressText.textContent = `${currentLayer} / ${totalLayers} layers`;

            // Update print status and button states
            if (isPrinting) {
                printStatusText.textContent = 'Printing';
                btnStartPrint.disabled = true;
                btnCancelPrint.disabled = false;
                // Disable manual controls during printing
                disableManualControls(true);
            } else {
                printStatusText.textContent = totalLayers > 0 ? 'Print Complete' : 'Ready';
                btnStartPrint.disabled = false;
                btnCancelPrint.disabled = true;
                // Enable manual controls when not printing
                disableManualControls(false);
            }
        }

        log('Status updated successfully.');
    }
    
    function handleConnectionError(error) {
        connectionStatus.className = 'status-dot-red';
        stateValue.textContent = 'ERROR';
        xPosValue.textContent = 'N/A';
        zPosValue.textContent = 'N/A';
        gripperStateValue.textContent = 'N/A';
        gripperCurrentState.textContent = 'Unknown';
        printProgressValue.textContent = 'N/A';
        printProgressText.textContent = 'N/A';
        printStatusText.textContent = 'Connection Error';
        log(`Connection Error: ${error.message || 'Unknown error'}`, 'error');
    }

    function log(message, type = 'info') {
        logPanel.textContent = message;
        logPanel.className = type === 'error' ? 'log-error' : 'log-info';
    }

    // Input validation function
    function validateInput(value, min, max, fieldName) {
        if (isNaN(value)) {
            log(`${fieldName} must be a valid number`, 'error');
            return false;
        }
        if (value < min || value > max) {
            log(`${fieldName} must be between ${min} and ${max}`, 'error');
            return false;
        }
        return true;
    }

    // Tab switching function
    function switchTab(targetTab) {
        // Remove active class from all tabs and contents
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${targetTab}"]`).classList.add('active');
        document.getElementById(`${targetTab}-tab`).classList.add('active');
    }

    // Disable/enable manual controls during printing
    function disableManualControls(disable) {
        btnMoveX.disabled = disable;
        btnMoveZ.disabled = disable;
        btnSetZZero.disabled = disable;
        btnOpenGripper.disabled = disable;
        btnCloseGripper.disabled = disable;
        btnHomeX.disabled = disable;
        btnHomeZ.disabled = disable;
    }

    // Update build area configuration visibility
    function updateBuildAreaConfig() {
        const selectedShape = document.querySelector('input[name="build-area-shape"]:checked').value;
        if (selectedShape === 'circle') {
            circleConfig.classList.remove('hidden');
            rectangleConfig.classList.add('hidden');
        } else {
            circleConfig.classList.add('hidden');
            rectangleConfig.classList.remove('hidden');
        }
    }

    // Update file upload status
    function updateFileStatus(drumId, file) {
        if (file) {
            drumStatusSpans[drumId].textContent = `Selected: ${file.name}`;
            drumStatusSpans[drumId].className = 'upload-status';
        } else {
            drumStatusSpans[drumId].textContent = 'No file selected';
            drumStatusSpans[drumId].className = 'upload-status';
        }
    }

    // --- Event Listeners ---
    btnEstop.addEventListener('click', () => {
        if (confirm('Are you sure you want to perform an EMERGENCY STOP?')) {
            sendCommand('/api/emergency_stop');
        }
    });

    btnHomeX.addEventListener('click', () => sendCommand('/api/home_x'));
    btnHomeZ.addEventListener('click', () => sendCommand('/api/home_z'));

    // Phase 2 Event Listeners
    btnMoveX.addEventListener('click', moveXAxis);
    btnMoveZ.addEventListener('click', moveZAxis);
    btnSetZZero.addEventListener('click', () => {
        if (confirm('Set current Z position as zero? This will affect all future Z movements.')) {
            setZZero();
        }
    });

    btnOpenGripper.addEventListener('click', () => controlGripper('open'));
    btnCloseGripper.addEventListener('click', () => controlGripper('close'));

    // Input validation on keyup for immediate feedback
    [xPositionInput, xSpeedInput, zPositionInput, zSpeedInput].forEach(input => {
        input.addEventListener('input', () => {
            const value = parseFloat(input.value);
            if (isNaN(value)) {
                input.style.borderColor = '#e74c3c';
            } else {
                input.style.borderColor = '#bdc3c7';
            }
        });
    });

    // Phase 3 Event Listeners

    // Tab switching
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });

    // Build area configuration
    buildAreaShapeRadios.forEach(radio => {
        radio.addEventListener('change', updateBuildAreaConfig);
    });

    // File upload handling
    drumFileInputs.forEach((input, drumId) => {
        input.addEventListener('change', () => {
            updateFileStatus(drumId, input.files[0]);
        });
    });

    // Geometry upload buttons
    drumUploadButtons.forEach((button, drumId) => {
        button.addEventListener('click', () => {
            const file = drumFileInputs[drumId].files[0];
            if (!file) {
                log(`Please select a file for drum ${drumId}`, 'error');
                return;
            }
            uploadGeometry(drumId, file);
        });
    });

    // Print parameter validation
    [layerStartInput, layerEndInput, patterningSpeedInput, travelSpeedInput, layerThicknessInput].forEach(input => {
        input.addEventListener('input', () => {
            const value = parseFloat(input.value);
            if (isNaN(value) || value <= 0) {
                input.style.borderColor = '#e74c3c';
            } else {
                input.style.borderColor = '#bdc3c7';
            }
        });
    });

    // Validation for offset parameters (can be negative)
    [xOffsetInput, zOffsetParamInput].forEach(input => {
        input.addEventListener('input', () => {
            const value = parseFloat(input.value);
            if (isNaN(value)) {
                input.style.borderColor = '#e74c3c';
            } else {
                input.style.borderColor = '#bdc3c7';
            }
        });
    });

    // Validation for collectors delay (non-negative integer)
    collectorsDelayInput.addEventListener('input', () => {
        const value = parseInt(collectorsDelayInput.value);
        if (isNaN(value) || value < 0) {
            collectorsDelayInput.style.borderColor = '#e74c3c';
        } else {
            collectorsDelayInput.style.borderColor = '#bdc3c7';
        }
    });

    // Print control buttons
    btnSetParameters.addEventListener('click', setPrintParameters);
    btnStartPrint.addEventListener('click', startPrint);
    btnCancelPrint.addEventListener('click', cancelPrint);

    // --- Initialization ---

    // Initialize build area configuration
    updateBuildAreaConfig();

    // Initialize file upload status
    drumFileInputs.forEach((input, drumId) => {
        updateFileStatus(drumId, null);
    });

    // Start status polling
    setInterval(pollStatus, POLLING_INTERVAL);
    pollStatus(); // Initial call
});
