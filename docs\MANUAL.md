# Connection Manual - Step-by-Step Setup Guide

## 🎯 Overview

This manual will guide you through connecting your IPC (computer) directly to the recoater system using a USB3-to-RJ45 adapter, completely bypassing the Controllino PLC.

## 📋 What You'll Need

### Hardware Requirements:
- [ ] Your IPC/computer with USB3 port
- [ ] USB3-to-RJ45 Ethernet adapter
- [ ] Ethernet cable (Cat5e or Cat6)
- [ ] Access to the recoater system

### Software Requirements:
- [ ] Python 3.7 or newer
- [ ] Administrator/root access on your computer
- [ ] Text editor (Notepad++, VS Code, or similar)

---

## 🔧 Step 1: Hardware Setup

### 1.1 Connect the USB3-to-RJ45 Adapter

1. **Plug the USB3-to-RJ45 adapter** into a USB3 port on your computer
2. **Wait for driver installation** (Windows should install drivers automatically)
3. **Verify the adapter is recognized**:
   - Windows: Check Device Manager → Network adapters
   - Look for "USB Ethernet" or similar device

### 1.2 Connect to Recoater System

1. **Locate the recoater's network port** (usually RJ45 Ethernet connector)
2. **Connect ethernet cable** from your USB adapter to the recoater
3. **Power on the recoater system** and wait for it to fully boot

### 1.3 Verify Physical Connection

**Check the LED indicators:**
- **USB adapter**: Should show power/activity lights
- **Recoater network port**: Should show link/activity lights
- **Both ends**: Should have solid or blinking lights indicating connection

---

## 🌐 Step 2: Network Configuration

### 2.1 Find Your New Network Interface

**Windows:**
1. Open **Control Panel** → **Network and Internet** → **Network Connections**
2. Look for a new "Ethernet" connection (usually named "Ethernet 2" or similar)
3. Note the name - you'll need it for configuration

**Alternative method:**
1. Open **Command Prompt** as Administrator
2. Type: `ipconfig /all`
3. Look for the USB Ethernet adapter

### 2.2 Configure Static IP Address

**Why we need this:** The recoater expects to communicate on a specific network range.

**Windows Configuration:**
1. **Right-click** on your new Ethernet connection
2. Select **Properties**
3. Select **Internet Protocol Version 4 (TCP/IPv4)**
4. Click **Properties**
5. Select **Use the following IP address**
6. Enter these settings:
   ```
   IP address: ***********00
   Subnet mask: *************
   Default gateway: (leave blank)
   DNS servers: (leave blank)
   ```
7. Click **OK** to save

### 2.3 Test Basic Network Connectivity

1. **Open Command Prompt**
2. **Test your own interface**: `ping ***********00`
   - Should get replies from your own computer
3. **Test recoater connection**: `ping 127.0.0.1`
   - This tests if the recoater API is responding

---

## 🐍 Step 3: Python Environment Setup

### 3.1 Verify Python Installation

1. **Open Command Prompt**
2. **Check Python version**: `python --version`
   - Should show Python 3.7 or newer
   - If not installed, download from [python.org](https://python.org)

### 3.2 Navigate to Project Directory

```bash
# Change to your project directory
cd C:\path\to\RecoaterSearch\APIRecoater

# Verify you're in the right place
dir
# Should see: config.py, simple_connection_test.py, requirements.txt
```

### 3.3 Install Required Libraries

```bash
# Install the requests library
pip install -r requirements.txt

# Verify installation
python -c "import requests; print('Requests library installed successfully')"
```

---

## 🔍 Step 4: Configuration

### 4.1 Update config.py

1. **Open config.py** in a text editor
2. **Verify these settings**:
   ```python
   RECOATER_API_ADDRESS = "127.0.0.1"  # Localhost
   RECOATER_API_PORT = 8080             # Standard API port
   API_TIMEOUT = 10.0                   # 10 second timeout
   ```
3. **Save the file**

### 4.2 Understanding the Configuration

- **127.0.0.1**: Special "localhost" address meaning "this computer"
- **Port 8080**: The recoater's API server listens on this port
- **Timeout**: How long to wait for responses before giving up

---

## ✅ Step 5: Connection Test

### 5.1 Run the Basic Connection Test

```bash
# Run the simple connection test
python simple_connection_test.py
```

### 5.2 Expected Results

**SUCCESS (what you want to see):**
```
==================================================
RECOATER CONNECTION TEST
==================================================
Trying to connect to: http://127.0.0.1:8080/api/v3

Step 1: Sending request to /state endpoint...
Step 2: Got response with status code: 200
✅ SUCCESS! Connection to recoater established!

Recoater System Information:
------------------------------
System State: ready

Raw API Response:
{
  "state": "ready"
}

==================================================
🎉 CONGRATULATIONS!
Your connection to the recoater is working!
You can now proceed to more advanced programs.
==================================================
```

**If you see this - YOU'RE DONE! 🎉**

---

## 🖥️ Step 6: Using the APIRecoater Web GUI

### 6.1 Overview

The APIRecoater Web GUI provides a modern, user-friendly interface for monitoring and controlling the recoater system. It replaces the cumbersome SwaggerUI with an intuitive web-based control panel.

### 6.2 Starting the GUI

1. **Navigate to the GUI directory**:
   ```bash
   cd APIRecoater-GUI
   ```

2. **Install GUI dependencies** (first time only):
   ```bash
   pip install -r requirements.txt
   ```

3. **Start the web server**:
   ```bash
   python app.py
   ```

4. **Expected output**:
   ```
   RecoaterClient initialized with URL: http://*************:8080/api/v3
   * Serving Flask app 'app'
   * Debug mode: on
   * Running on all addresses (0.0.0.0)
   * Running on http://127.0.0.1:5001
   * Running on http://***********:5001
   ```

### 6.3 Accessing the GUI

1. **Open your web browser** (Firefox recommended)
2. **Navigate to**: `http://127.0.0.1:5001`
3. **The GUI should load** showing the APIRecoater Control Panel

### 6.4 GUI Features

The APIRecoater Web GUI provides a comprehensive interface with two main tabs:

#### Control Tab - System Monitoring and Manual Control

**System Status Panel:**
- **Connection Status**: Green dot = connected, Red dot = disconnected
- **System State**: Current recoater state (ready, busy, error, etc.)
- **X-Axis Position**: Current X-axis position in mm
- **X-Axis Homed**: Whether X-axis has been homed
- **Z-Axis Position**: Current Z-axis position in mm
- **Z-Axis Homed**: Whether Z-axis has been homed
- **Gripper State**: Current gripper state (open/closed)
- **Z-Offset**: Current Z-offset for relative positioning
- **Print Progress**: Current layer / Total layers during printing

**Emergency Controls:**
- **EMERGENCY STOP**: Immediately stops all motion (requires confirmation)
- **Home X-Axis**: Moves X-axis to its home position
- **Home Z-Axis**: Moves Z-axis to its home position

**Manual Control Panel:**
- **X-Axis Control**: Move to specific position with custom speed
- **Z-Axis Control**: Move to specific position with custom speed
- **Set Current Z as Zero**: Establish new Z-offset for relative positioning
- **Gripper Control**: Open/close gripper with real-time state feedback

#### Print Setup Tab - Complete Print Job Management

**Build Area Configuration:**
- **Shape Selection**: Choose between Circle or Rectangle build area
- **Dimensions**: Set diameter for circle or width/height for rectangle
- **Visual Feedback**: Configuration updates dynamically

**Geometry Upload:**
- **3-Drum Support**: Upload geometry files to each drum independently
- **File Formats**: Supports PNG, CLI, and STL files
- **Upload Status**: Real-time feedback on upload success/failure
- **File Validation**: Automatic file size and format checking

**Print Parameters:**
- **Layer Range**: Set start and end layers for the print job
- **Filling Drum**: Select which drum (0, 1, or 2) to use for filling
- **Speed Control**: Configure patterning speed and travel speed
- **Powder Saving**: Enable/disable powder saving mode
- **Parameter Validation**: Real-time input validation with visual feedback

**Print Control:**
- **Start Print**: Begin print job with confirmation dialog
- **Cancel Print**: Stop current print job with confirmation
- **Print Status**: Real-time display of print status and progress
- **Context-Aware UI**: Manual controls disabled during printing

#### Real-time Updates
- **Automatic Polling**: Status updates every 2 seconds
- **Live Data**: All information reflects current recoater state
- **Error Handling**: Connection issues are clearly displayed
- **State Management**: UI adapts based on system state (printing/idle)

### 6.5 Using the Control Tab

#### Emergency Stop
1. **Click the red "EMERGENCY STOP" button**
2. **Confirm the action** in the popup dialog
3. **All motion will stop immediately**

#### Homing Axes
1. **Click "Home X-Axis"** or **"Home Z-Axis"**
2. **The axis will move to its reference position**
3. **Watch the status panel** for position updates
4. **"Homed" status will change to "true"** when complete

#### Manual Axis Movement
1. **Enter desired position** in the position field (mm)
2. **Set movement speed** in the speed field (mm/s)
3. **Click "Move X"** or **"Move Z"** to execute movement
4. **Watch status panel** for real-time position updates

**Input Validation:**
- X-axis: Position 0-1000mm, Speed 1-100mm/s
- Z-axis: Position -100-100mm, Speed 0.1-20mm/s
- Invalid inputs are highlighted in red

#### Z-Offset Management
1. **Move Z-axis to desired zero position** using manual controls
2. **Click "Set Current Z as Zero"** button
3. **Confirm the action** in the popup dialog
4. **All future Z movements will be relative to this new zero**
5. **Z-Offset value updates** in the status panel

#### Gripper Control
1. **Click "Open Gripper"** or **"Close Gripper"**
2. **Watch gripper state** update in real-time
3. **Status shows in both** status panel and manual control section

### 6.6 Using the Print Setup Tab

#### Build Area Configuration
1. **Click the "Print Setup" tab** to switch views
2. **Select build area shape**: Circle or Rectangle
3. **Enter dimensions**:
   - **Circle**: Diameter in mm
   - **Rectangle**: Width and Height in mm
4. **Configuration updates automatically**

#### Uploading Geometry Files
1. **For each drum (0, 1, 2)**:
   - **Click "Choose File"** button
   - **Select geometry file** (PNG, CLI, or STL format)
   - **File name appears** in the status area
   - **Click "Upload to Drum X"** button
2. **Monitor upload status**:
   - **"Upload successful"** in green for success
   - **"Upload failed"** in red with error message
3. **Repeat for all required drums**

**File Requirements:**
- Maximum file size: 50MB
- Supported formats: PNG, CLI only (STL not supported)
- Each drum can have one geometry file
- Filenames are automatically sanitized for security

#### Setting Print Parameters
1. **Configure layer range**:
   - **Layer Start**: First layer to print (minimum 1)
   - **Layer End**: Last layer to print (must be ≥ start)
2. **Select filling drum**: Choose "No Filling" (-1), or drum 0, 1, or 2
3. **Set speeds**:
   - **Patterning Speed**: Speed during pattern creation (mm/s)
   - **Travel Speed**: Speed during non-printing moves (mm/s)
4. **Configure offsets**:
   - **X Offset**: X-axis offset in mm (can be negative)
   - **Z Offset**: Z-axis offset in mm (can be negative)
5. **Set layer parameters**:
   - **Layer Thickness**: Thickness of each layer in mm (default: 0.08mm)
   - **Collectors Delay**: Delay for collectors in milliseconds
6. **Enable Powder Saving** if desired (checkbox)
7. **Click "Set Print Parameters"** to apply settings

**Parameter Validation:**
- Layer numbers must be positive integers
- Speeds must be positive values
- Invalid inputs are highlighted in red
- Success/error messages appear in the log

#### Starting and Managing Print Jobs
1. **Ensure all prerequisites**:
   - Geometry files uploaded to required drums
   - Print parameters set correctly
   - System in "ready" state
2. **Click "Start Print"** button
3. **Confirm in popup dialog**
4. **Monitor print progress**:
   - **Print Status**: Shows "Printing" during job
   - **Progress**: Shows "Current Layer / Total Layers"
   - **Manual controls disabled** during printing
5. **To cancel print**:
   - **Click "Cancel Print"** button (enabled during printing)
   - **Confirm cancellation** in popup dialog

#### Print Status Monitoring
- **Print Status Text**: Ready, Printing, Print Complete, or error states
- **Progress Display**: Real-time layer progress (e.g., "25 / 100 layers")
- **Button States**: Start/Cancel buttons enable/disable based on print state
- **Manual Control Lockout**: Manual controls disabled during active printing

### 6.6 Network Access

The GUI server runs on all network interfaces, allowing access from other computers:

- **Local access**: `http://127.0.0.1:5001`
- **Network access**: `http://[your-computer-ip]:5001`
- **Find your IP**: Use `ipconfig` command

### 6.7 Stopping the GUI

- **Press Ctrl+C** in the terminal where the server is running
- **The web interface will become unavailable**
- **No data is lost** - restart anytime with `python app.py`

---

## 🚨 Troubleshooting

### Problem: "CONNECTION FAILED - Could not connect"

**Possible Causes & Solutions:**

1. **Recoater system not running**
   - Check if recoater is powered on
   - Verify all systems have finished booting
   - Look for status lights indicating "ready"

2. **Network configuration wrong**
   - Double-check IP address settings
   - Try `ping 127.0.0.1` in command prompt
   - Verify USB adapter is working

3. **USB3-to-RJ45 adapter issues**
   - Try a different USB port
   - Check Device Manager for driver issues
   - Test adapter with another device

4. **Firewall blocking connection**
   - Temporarily disable Windows Firewall
   - Add exception for Python or port 8080
   - Check antivirus software

### Problem: "TIMEOUT - System didn't respond"

**Solutions:**
1. **Increase timeout** in config.py: `API_TIMEOUT = 30.0`
2. **Wait longer** - recoater might be busy
3. **Check system load** - recoater might be overloaded

### Problem: "Wrong IP address in config.py"

**Solutions:**
1. **Verify recoater IP**: Check recoater documentation
2. **Try different addresses**:
   - `127.0.0.1` (localhost)
   - `***********` (common router address)
   - Check recoater's display/settings for its IP

### Problem: Python or pip not found

**Solutions:**
1. **Install Python** from [python.org](https://python.org)
2. **Add Python to PATH** during installation
3. **Use full path**: `C:\Python39\python.exe` instead of just `python`

### GUI-Specific Problems

#### Problem: "Flask not found" or "Module not found"

**Solutions:**
1. **Install GUI dependencies**:
   ```bash
   cd APIRecoater-GUI
   pip install -r requirements.txt
   ```
2. **Verify installation**: `pip list | findstr Flask`

#### Problem: "Address already in use" or "Port 5001 busy"

**Solutions:**
1. **Stop existing server**: Press Ctrl+C in terminal
2. **Kill process**: `taskkill /f /im python.exe` (Windows)
3. **Use different port**: Edit `app.py` and change `port=5001` to `port=5002`

#### Problem: GUI loads but shows "Connection Error"

**Solutions:**
1. **Check recoater connection**: Run `python simple_connection_test.py` first
2. **Verify config.py**: Ensure correct IP address in both main directory and GUI directory
3. **Check network**: Recoater must be powered on and accessible

#### Problem: Browser can't access GUI

**Solutions:**
1. **Check server is running**: Look for "Running on http://127.0.0.1:5001" message
2. **Try different browser**: Firefox is recommended
3. **Check firewall**: Allow Python through Windows Firewall
4. **Use correct URL**: `http://127.0.0.1:5001` (not https)

#### Problem: GUI freezes or becomes unresponsive

**Solutions:**
1. **Refresh browser page**: F5 or Ctrl+R
2. **Restart server**: Ctrl+C then `python app.py`
3. **Clear browser cache**: Ctrl+Shift+Delete
4. **Check server logs**: Look for error messages in terminal

### Phase 3 Print Setup Problems

#### Problem: "File upload failed" or "File too large"

**Solutions:**
1. **Check file size**: Maximum 50MB per file
2. **Verify file format**: Use PNG, CLI, or STL files only
3. **Check file permissions**: Ensure file is not locked or in use
4. **Try smaller file**: Compress or reduce geometry complexity
5. **Check disk space**: Ensure sufficient space on recoater system

#### Problem: "Invalid print parameters" or parameter validation errors

**Solutions:**
1. **Check layer range**: Start layer ≥ 1, End layer ≥ Start layer
2. **Verify speeds**: All speeds must be positive numbers
3. **Check drum ID**: Filling drum must be 0, 1, or 2
4. **Clear invalid inputs**: Delete and re-enter problematic values
5. **Check number formats**: Use decimal point (.) not comma (,)

#### Problem: "Cannot start print job" or print start fails

**Solutions:**
1. **Upload geometries first**: Ensure required drums have geometry files
2. **Set parameters**: Click "Set Print Parameters" before starting
3. **Check system state**: System must be in "ready" state
4. **Verify connection**: Ensure recoater is connected and responsive
5. **Check for errors**: Look for error messages in status panel

#### Problem: Print progress not updating or stuck

**Solutions:**
1. **Check recoater status**: Verify print job is actually running
2. **Refresh browser**: F5 to reload page and update status
3. **Check connection**: Ensure stable connection to recoater
4. **Monitor server logs**: Look for API communication errors
5. **Restart if needed**: Cancel print and restart if truly stuck

#### Problem: Manual controls disabled when not printing

**Solutions:**
1. **Check print status**: Ensure no print job is active
2. **Cancel any active job**: Use "Cancel Print" if job is stuck
3. **Refresh page**: F5 to reset UI state
4. **Check system state**: System should show "ready" when idle
5. **Restart server**: Ctrl+C and restart if UI state is corrupted

#### Problem: Tab switching not working

**Solutions:**
1. **Click tab buttons**: Ensure you're clicking the tab buttons at the top
2. **Check JavaScript**: Ensure browser has JavaScript enabled
3. **Try different browser**: Firefox is recommended
4. **Clear cache**: Ctrl+Shift+Delete to clear browser cache
5. **Check console**: F12 → Console tab for JavaScript errors

---

## 🔧 Advanced Configuration

### Custom IP Addresses

If your recoater uses a different IP address:

1. **Find the recoater's IP address**:
   - Check recoater's display/menu
   - Look at existing network configuration
   - Ask system administrator

2. **Update config.py**:
   ```python
   RECOATER_API_ADDRESS = "***********"  # Example: use actual IP
   ```

3. **Update your computer's IP** to match the same network:
   ```
   IP address: *************
   Subnet mask: *************
   ```

### Port Configuration

If the API runs on a different port:

1. **Check recoater documentation** for correct port
2. **Update config.py**:
   ```python
   RECOATER_API_PORT = 8081  # Example: use actual port
   ```

---

## ✅ Verification Checklist

Before proceeding to advanced programs, verify:

### Basic Connection
- [ ] USB3-to-RJ45 adapter is connected and recognized
- [ ] Ethernet cable connects your adapter to recoater
- [ ] Network interface is configured with correct IP
- [ ] Python and requests library are installed
- [ ] config.py has correct IP address and port
- [ ] simple_connection_test.py shows "SUCCESS"
- [ ] You can see recoater status information

### GUI Verification
- [ ] Flask and GUI dependencies are installed
- [ ] `python app.py` starts without errors
- [ ] Browser can access http://127.0.0.1:5001
- [ ] GUI shows connection status (red dot if recoater disconnected)
- [ ] Status panel updates every 2 seconds
- [ ] Control buttons are visible and clickable
- [ ] Emergency stop button shows confirmation dialog

### Phase 3 Print Setup Verification
- [ ] Tab navigation works (Control ↔ Print Setup)
- [ ] Build area configuration options are visible
- [ ] All 3 drum upload sections are present
- [ ] Print parameters panel has all required fields
- [ ] Print control buttons (Start/Cancel) are visible
- [ ] File upload dialogs open when clicking "Choose File"
- [ ] Parameter validation works (red borders for invalid inputs)
- [ ] Print status updates in real-time
- [ ] Manual controls disable during simulated print state

### Complete System Verification
- [ ] **Control Tab**: All Phase 1 & 2 features working
- [ ] **Print Setup Tab**: All Phase 3 features accessible
- [ ] **Status Monitoring**: Real-time updates across all tabs
- [ ] **Error Handling**: Graceful handling of connection issues
- [ ] **Responsive Design**: Interface works on different screen sizes
- [ ] **Input Validation**: All forms validate user input properly
- [ ] **State Management**: UI adapts correctly to system state changes

**If all items are checked - you have a fully functional APIRecoater system!** 🚀

---

## 📞 Getting Help

### If you're still having problems:

1. **Check the error message carefully** - it usually tells you what's wrong
2. **Try each step again** - sometimes a small detail was missed
3. **Test one thing at a time** - isolate the problem
4. **Document what you tried** - helps with troubleshooting

### Common Commands for Diagnostics:

```bash
# Test network connectivity
ping 127.0.0.1
ping ***********

# Check network configuration
ipconfig /all

# Test Python installation
python --version
pip --version

# Test requests library
python -c "import requests; print('OK')"
```

Remember: Every expert was once a beginner having these same problems! Take your time and work through each step carefully.
