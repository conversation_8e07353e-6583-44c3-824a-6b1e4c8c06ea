/* Industrial-style CSS for APIRecoater Control Panel */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 1.8rem;
    font-weight: 600;
}

/* Main Content Layout */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
    max-width: 1600px;
    margin: 0 auto;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.status-dot-green {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #27ae60;
    display: inline-block;
    box-shadow: 0 0 6px rgba(39, 174, 96, 0.6);
}

.status-dot-red {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #e74c3c;
    display: inline-block;
    box-shadow: 0 0 6px rgba(231, 76, 60, 0.6);
}

/* Control panels layout */
#status-panel {
    grid-column: 1;
}

#control-panel {
    grid-column: 2;
}

#manual-control-panel {
    grid-column: 1 / -1; /* Span full width */
}

/* Print Setup panels layout */
#build-area-panel {
    grid-column: 1;
}

#geometry-panel {
    grid-column: 2;
}

#print-parameters-panel {
    grid-column: 1;
}

#print-control-panel {
    grid-column: 2;
}

.panel {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #ddd;
}

.panel h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.panel p {
    margin-bottom: 0.8rem;
    font-size: 1rem;
}

.panel strong {
    color: #2c3e50;
    font-weight: 600;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0.25rem;
}

button:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-danger {
    background-color: #e74c3c;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 1rem 2rem;
    margin-bottom: 1rem;
    width: 100%;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.control-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.control-group button {
    flex: 1;
    min-width: 120px;
}

/* Manual Control Panel Styles */
.axis-control {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fafafa;
}

.axis-control h3 {
    color: #2c3e50;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 0.3rem;
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    gap: 1rem;
}

.input-group label {
    min-width: 120px;
    font-weight: 500;
    color: #34495e;
}

.input-group input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 1rem;
    max-width: 150px;
}

.input-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-primary {
    background-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.gripper-status {
    margin-bottom: 0.8rem;
    padding: 0.5rem;
    background-color: #ecf0f1;
    border-radius: 4px;
    font-size: 0.95rem;
}

/* Print Setup Styles */
.config-group, .param-group {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.config-group label, .param-group label {
    min-width: 150px;
    font-weight: 500;
    color: #34495e;
}

.radio-group {
    display: flex;
    gap: 1rem;
}

.radio-group label {
    min-width: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hidden {
    display: none !important;
}

.drum-upload-section {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fafafa;
}

.drum-upload-section h3 {
    color: #2c3e50;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 0.3rem;
}

.upload-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.upload-status {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-style: italic;
}

.upload-status.success {
    color: #27ae60;
}

.upload-status.error {
    color: #e74c3c;
}

.print-status {
    background-color: #ecf0f1;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.print-status p {
    margin-bottom: 0.5rem;
}

select {
    padding: 0.5rem;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 1rem;
    background-color: white;
    min-width: 120px;
}

select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

input[type="file"] {
    padding: 0.5rem;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    background-color: white;
    font-size: 0.9rem;
}

footer {
    background-color: #34495e;
    color: white;
    padding: 1rem 2rem;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
}

#log-panel {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-error {
    color: #e74c3c;
}

.log-info {
    color: #ecf0f1;
}

/* Responsive design */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .control-group {
        flex-direction: column;
    }

    .input-group, .config-group, .param-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .input-group label, .config-group label, .param-group label {
        min-width: auto;
    }

    .input-group input {
        max-width: 100%;
    }

    .upload-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .radio-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    footer {
        position: relative;
        margin-top: 2rem;
    }
}
