# APIRecoater - Direct Recoater Control System

## 🎯 Project Overview

APIRecoater is a simplified control system that connects directly to the 3-drum recoater system, bypassing the Controllino PLC entirely. This project leverages the existing REST API infrastructure to provide direct control with minimal new code development.

## 🏗️ High-Level Architecture

### Current vs. Our Approach

```mermaid
graph TB
    subgraph "BEFORE: Traditional Control"
        IPC1[Your IPC] -.->|Complex| CTRL[Controllino PLC]
        CTRL -->|Multiple Protocols| RECOATER1[3-Drum Recoater]
    end
    
    subgraph "AFTER: Our Direct Control"
        IPC2[Your IPC] -->|USB3-to-RJ45| API[Recoater REST API]
        API -->|Direct Control| RECOATER2[3-Drum Recoater]
    end
    
    classDef old fill:#ffebee
    classDef new fill:#e8f5e8
    
    class IPC1,CTRL,RECOATER1 old
    class IPC2,API,RECOATER2 new
```

### System Components

```mermaid
graph LR
    subgraph "Your Computer (IPC)"
        PYTHON[Python Program<br/>APIRecoater]
        CONFIG[config.py<br/>Settings]
    end
    
    subgraph "Connection"
        USB[USB3 Port]
        ADAPTER[USB3-to-RJ45<br/>Adapter]
        CABLE[Ethernet Cable]
    end
    
    subgraph "Recoater System"
        API[REST API Server<br/>127.0.0.1:8080]
        HARDWARE[3-Drum Hardware<br/>Motors & Valves]
    end
    
    PYTHON -->|HTTP Requests| CONFIG
    PYTHON -->|USB3| USB
    USB -->|Ethernet| ADAPTER
    ADAPTER -->|RJ45| CABLE
    CABLE -->|TCP/IP| API
    API -->|Direct Control| HARDWARE
    
    classDef computer fill:#e3f2fd
    classDef connection fill:#fff3e0
    classDef recoater fill:#e8f5e8
    
    class PYTHON,CONFIG computer
    class USB,ADAPTER,CABLE connection
    class API,HARDWARE recoater
```

## 🎯 Design Philosophy

### Minimize New Code ✅
- **Reuse existing API**: The recoater already has a complete REST API
- **Standard libraries**: Only use Python's `requests` library
- **Copy proven patterns**: Adapt code from existing system

### Maximize Existing Functionality ✅
- **Complete API coverage**: Access all recoater functions via existing endpoints
- **Proven protocols**: Use the same HTTP/REST that the system already supports
- **Existing safety systems**: Leverage built-in safety checks

### Ignore Controllino Completely ✅
- **Direct API access**: Bypass PLC entirely
- **Simplified network**: Point-to-point connection
- **Reduced complexity**: No PLC programming needed

## 📋 Project Structure

```
APIRecoater/
├── docs/
│   ├── README.md          # This file - architectural overview
│   ├── TEXTBOOK.md        # Technology tutorial for beginners
│   └── MANUAL.md          # Step-by-step connection guide
├── config.py              # All configuration settings
├── simple_connection_test.py  # Basic connection test
├── requirements.txt       # Python dependencies
└── [Future files as we develop]
```

## 🚀 Getting Started (Quick Version)

### 1. Hardware Setup
1. Connect USB3-to-RJ45 adapter to your computer
2. Connect ethernet cable from adapter to recoater
3. Configure network settings (see MANUAL.md)

### 2. Software Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Test connection
python simple_connection_test.py
```

### 3. First Success
If you see "✅ SUCCESS! Connection to recoater established!" - you're ready to go!

## 🔧 Available API Functions

Based on the existing recoater API, we can control:

### Motion Control
- **X-Axis**: Linear movement (0-800mm range)
- **Z-Axis**: Vertical movement (0-120mm range) 
- **Gripper**: Z-axis gripper control

### Drum Operations
- **3-Drum System**: Independent control of each drum
- **Geometry Setup**: Load powder patterns
- **Pressure Control**: Ejection pressure management

### Print Jobs
- **Start/Stop**: Print job management
- **Parameters**: Speed, thickness, offsets
- **Status**: Real-time system monitoring

### System Monitoring
- **State**: Overall system status
- **Safety**: Emergency stop and interlock monitoring
- **Diagnostics**: Error detection and reporting

## 🛡️ Safety Features

### Built-in Safety (from existing API)
- Emergency stop monitoring
- Door/cover interlock checking
- Position limit enforcement
- Speed limit validation

### Our Additional Safety
- Configuration validation
- Connection timeout handling
- Error recovery procedures
- Safe default parameters

## 📚 Learning Path

### For Complete Beginners:
1. **Start here**: Read TEXTBOOK.md for technology basics
2. **Setup**: Follow MANUAL.md for hardware connection
3. **Test**: Run simple_connection_test.py
4. **Learn**: Study the code comments and examples
5. **Expand**: Build more complex programs step by step

### Key Concepts You'll Learn:
- **HTTP/REST APIs**: How programs talk to each other over networks
- **JSON**: Data format for sending information
- **TCP/IP**: How network connections work
- **Python requests**: Library for making web requests
- **Error handling**: What to do when things go wrong

## 🎯 Success Criteria

### Phase 1: Basic Connection ✅
- [x] Hardware connected via USB3-to-RJ45
- [x] Network configured correctly
- [x] simple_connection_test.py shows success
- [x] Can read system status

### Phase 2: Basic Control ✅
- [x] Move X-axis to specific positions
- [x] Move Z-axis up and down
- [x] Control gripper open/close
- [x] Read current positions

### Phase 3: Advanced Operations ✅
- [x] Load drum geometries
- [x] Start/stop print jobs
- [x] Monitor system status
- [x] Handle errors gracefully

## 🖥️ APIRecoater Web GUI

The project includes a modern web-based GUI that provides an intuitive interface for controlling the recoater system:

### Features
- **Tab-based Interface**: Separate tabs for Control and Print Setup
- **Real-time Monitoring**: Live status updates every 2 seconds
- **Manual Control**: Direct X/Z axis movement and gripper control
- **Print Job Management**: Complete print workflow from geometry upload to job execution
- **Responsive Design**: Works on desktop and mobile devices

### Control Tab
- **System Status**: Real-time display of all system parameters
- **Emergency Controls**: Emergency stop with confirmation
- **Axis Control**: Home X and Z axes
- **Manual Movement**: Precise positioning with speed control
- **Z-Offset Management**: Set virtual zero positions
- **Gripper Control**: Open/close gripper with state feedback

### Print Setup Tab
- **Build Area Configuration**: Circle or rectangle build area setup
- **Geometry Upload**: Upload files to each of the 3 drums
- **Print Parameters**: Configure layer range, speeds, and options
- **Print Control**: Start/cancel print jobs with progress monitoring

### Getting Started with the GUI
1. Navigate to `APIRecoater-GUI` directory
2. Install dependencies: `pip install -r requirements.txt`
3. Start the server: `python app.py`
4. Open browser to: `http://127.0.0.1:5001`

## 🔗 Related Documentation

- **TEXTBOOK.md**: Complete beginner tutorial on all technologies
- **MANUAL.md**: Step-by-step hardware setup and connection guide
- **Original Analysis**: See `DOC/ReverseEngineering/reverse_engineering.md`

## 💡 Why This Approach Works

1. **Proven Technology**: The REST API already exists and works
2. **Simple Connection**: USB3-to-RJ45 is standard and reliable
3. **Minimal Learning**: Only need to understand HTTP requests
4. **Maximum Reuse**: Leverage all existing functionality
5. **Easy Debugging**: HTTP requests are easy to monitor and debug

---

**Next Steps**: Read TEXTBOOK.md to understand the technologies, then follow MANUAL.md to set up your connection!
