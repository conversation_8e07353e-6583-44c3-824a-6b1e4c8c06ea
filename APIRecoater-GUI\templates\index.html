<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APIRecoater Control</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <header>
        <h1>APIRecoater Control Panel</h1>
        <div class="status-indicator">
            Connection: <span id="connection-status" class="status-dot-red"></span>
        </div>
    </header>
    <main>
        <!-- System Monitoring and Basic Control Section -->
            <div class="panel" id="status-panel">
                <h2>System Status</h2>
                <p>State: <strong id="state-value">N/A</strong></p>
                <p>X-Axis Position: <strong id="x-pos-value">N/A</strong></p>
                <p>X-Axis Homed: <strong id="x-homed-value">N/A</strong></p>
                <p>Z-Axis Position: <strong id="z-pos-value">N/A</strong></p>
                <p>Z-Axis Homed: <strong id="z-homed-value">N/A</strong></p>
                <p>Gripper State: <strong id="gripper-state-value">N/A</strong></p>
                <p>Z-Offset: <strong id="z-offset-value">0.0</strong> mm</p>
                <p>Print Progress: <strong id="print-progress-value">0 / 0</strong></p>
            </div>
        <div class="panel" id="control-panel">
            <h2>Controls</h2>
            <button id="btn-estop" class="btn-danger">EMERGENCY STOP</button>
            <div class="control-group">
                <button id="btn-home-x">Home X-Axis</button>
                <button id="btn-home-z">Home Z-Axis</button>
            </div>
        </div>
        <div class="panel" id="manual-control-panel">
            <h2>Manual Control</h2>

            <!-- X-Axis Controls -->
            <div class="axis-control">
                <h3>X-Axis Control</h3>
                <div class="input-group">
                    <label for="x-position">Position (mm):</label>
                    <input type="number" id="x-position" min="0" max="1000" step="0.1" value="0">
                </div>
                <div class="input-group">
                    <label for="x-speed">Speed (mm/s):</label>
                    <input type="number" id="x-speed" min="1" max="100" step="1" value="30">
                </div>
                <button id="btn-move-x" class="btn-primary">Move X</button>
            </div>

            <!-- Z-Axis Controls -->
            <div class="axis-control">
                <h3>Z-Axis Control</h3>
                <div class="input-group">
                    <label for="z-position">Position (mm):</label>
                    <input type="number" id="z-position" min="-100" max="100" step="0.1" value="0">
                </div>
                <div class="input-group">
                    <label for="z-speed">Speed (mm/s):</label>
                    <input type="number" id="z-speed" min="0.1" max="20" step="0.1" value="5">
                </div>
                <div class="control-group">
                    <button id="btn-move-z" class="btn-primary">Move Z</button>
                    <button id="btn-set-z-zero" class="btn-secondary">Set Current Z as Zero</button>
                </div>
            </div>

            <!-- Gripper Controls -->
            <div class="axis-control">
                <h3>Gripper Control</h3>
                <div class="gripper-status">
                    Current State: <strong id="gripper-current-state">Unknown</strong>
                </div>
                <div class="control-group">
                    <button id="btn-open-gripper" class="btn-secondary">Open Gripper</button>
                    <button id="btn-close-gripper" class="btn-secondary">Close Gripper</button>
                </div>
            </div>

        <!-- Print Job Setup and Management Section -->
        <!-- Build Area Configuration -->
            <div class="panel" id="build-area-panel">
                <h2>Build Area Configuration</h2>
                <div class="config-group">
                    <label>Build Area Shape:</label>
                    <div class="radio-group">
                        <label><input type="radio" name="build-area-shape" value="circle" checked> Circle</label>
                        <label><input type="radio" name="build-area-shape" value="rectangle"> Rectangle</label>
                    </div>
                </div>
                <div class="config-group" id="circle-config">
                    <label for="circle-diameter">Diameter (mm):</label>
                    <input type="number" id="circle-diameter" min="1" max="500" value="100" step="0.1">
                </div>
                <div class="config-group hidden" id="rectangle-config">
                    <label for="rect-width">Width (mm):</label>
                    <input type="number" id="rect-width" min="1" max="500" value="100" step="0.1">
                    <label for="rect-height">Height (mm):</label>
                    <input type="number" id="rect-height" min="1" max="500" value="100" step="0.1">
                </div>
            </div>

            <!-- Geometry Upload -->
            <div class="panel" id="geometry-panel">
                <h2>Geometry Upload</h2>
                <div class="drum-upload-section">
                    <h3>Drum 0</h3>
                    <div class="upload-group">
                        <input type="file" id="drum-0-file" accept=".png,.cli">
                        <button id="btn-upload-drum-0" class="btn-secondary">Upload to Drum 0</button>
                        <span id="drum-0-status" class="upload-status">No file selected</span>
                    </div>
                </div>
                <div class="drum-upload-section">
                    <h3>Drum 1</h3>
                    <div class="upload-group">
                        <input type="file" id="drum-1-file" accept=".png,.cli">
                        <button id="btn-upload-drum-1" class="btn-secondary">Upload to Drum 1</button>
                        <span id="drum-1-status" class="upload-status">No file selected</span>
                    </div>
                </div>
                <div class="drum-upload-section">
                    <h3>Drum 2</h3>
                    <div class="upload-group">
                        <input type="file" id="drum-2-file" accept=".png,.cli">
                        <button id="btn-upload-drum-2" class="btn-secondary">Upload to Drum 2</button>
                        <span id="drum-2-status" class="upload-status">No file selected</span>
                    </div>
                </div>
            </div>

            <!-- Print Parameters -->
            <div class="panel" id="print-parameters-panel">
                <h2>Print Parameters</h2>
                <div class="param-group">
                    <label for="layer-start">Layer Start:</label>
                    <input type="number" id="layer-start" min="1" value="1" step="1">
                </div>
                <div class="param-group">
                    <label for="layer-end">Layer End:</label>
                    <input type="number" id="layer-end" min="1" value="100" step="1">
                </div>
                <div class="param-group">
                    <label for="filling-drum-id">Filling Drum ID:</label>
                    <select id="filling-drum-id">
                        <option value="-1">No Filling</option>
                        <option value="0">Drum 0</option>
                        <option value="1">Drum 1</option>
                        <option value="2">Drum 2</option>
                    </select>
                </div>
                <div class="param-group">
                    <label for="patterning-speed">Patterning Speed (mm/s):</label>
                    <input type="number" id="patterning-speed" min="1" max="200" value="30" step="1">
                </div>
                <div class="param-group">
                    <label for="travel-speed">Travel Speed (mm/s):</label>
                    <input type="number" id="travel-speed" min="1" max="500" value="100" step="1">
                </div>
                <div class="param-group">
                    <label for="x-offset">X Offset (mm):</label>
                    <input type="number" id="x-offset" min="-100" max="100" value="0" step="0.1">
                </div>
                <div class="param-group">
                    <label for="z-offset-param">Z Offset (mm):</label>
                    <input type="number" id="z-offset-param" min="-100" max="100" value="0" step="0.1">
                </div>
                <div class="param-group">
                    <label for="layer-thickness">Layer Thickness (mm):</label>
                    <input type="number" id="layer-thickness" min="0.01" max="1.0" value="0.08" step="0.01">
                </div>
                <div class="param-group">
                    <label for="collectors-delay">Collectors Delay (ms):</label>
                    <input type="number" id="collectors-delay" min="0" max="10000" value="0" step="1">
                </div>
                <div class="param-group">
                    <label>
                        <input type="checkbox" id="powder-saving"> Powder Saving
                    </label>
                </div>
                <button id="btn-set-parameters" class="btn-primary">Set Print Parameters</button>
            </div>

            <!-- Print Control -->
            <div class="panel" id="print-control-panel">
                <h2>Print Control</h2>
                <div class="print-status">
                    <p>Print Status: <strong id="print-status-text">Ready</strong></p>
                    <p>Progress: <strong id="print-progress-text">0 / 0 layers</strong></p>
                </div>
                <div class="control-group">
                    <button id="btn-start-print" class="btn-primary">Start Print</button>
                    <button id="btn-cancel-print" class="btn-danger" disabled>Cancel Print</button>
                </div>
            </div>
        </div>
    </main>
    <footer>
        <div id="log-panel">Log: Waiting for connection...</div>
    </footer>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
