from flask import Flask, render_template, jsonify, request
from recoater_client import <PERSON>coater<PERSON>lient
from werkzeug.utils import secure_filename
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

app = Flask(__name__)
client = RecoaterClient()

# Global variable to store Z-offset for Phase 2
z_offset = 0.0

@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('index.html')

@app.route('/api/status', methods=['GET'])
def get_status():
    """Endpoint for the frontend to poll for status updates."""
    try:
        if not client.test_connection():
             raise ConnectionError("Failed to connect to recoater")

        state = client.get_state()
        x_info = client.get_x_info()
        z_info = client.get_z_info()
        gripper_info = client.get_gripper_state()

        # Phase 3: Get print info
        try:
            print_info = client.get_print_info()
        except:
            print_info = {"n_layers": 0, "last_layer": 0}

        status_data = {
            "connection": "ok",
            "state": state.get('state', 'unknown'),
            "x": {
                "position": x_info.get('position'),
                "homed": x_info.get('homed')
            },
            "z": {
                "position": z_info.get('position'),
                "homed": z_info.get('homed')
            },
            "gripper": {
                "state": gripper_info.get('state', 'unknown')
            },
            "z_offset": z_offset,
            "print": {
                "current_layer": print_info.get('last_layer', 0),
                "total_layers": print_info.get('n_layers', 0),
                "is_printing": print_info.get('n_layers', 0) > 0 and print_info.get('last_layer', 0) < print_info.get('n_layers', 0)
            }
        }
        return jsonify(status_data)

    except Exception as e:
        logging.error(f"Error getting status: {e}")
        return jsonify({"connection": "error", "message": str(e)}), 500

# --- Phase 1 Control Endpoints ---

@app.route('/api/emergency_stop', methods=['POST'])
def emergency_stop():
    """Stops all motion."""
    try:
        result = client.emergency_stop()
        return jsonify({"success": True, "details": result})
    except Exception as e:
        logging.error(f"Error during emergency stop: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/home_x', methods=['POST'])
def home_x():
    """Homes the X-axis."""
    try:
        success = client.home_x_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing X-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500
        
@app.route('/api/home_z', methods=['POST'])
def home_z():
    """Homes the Z-axis."""
    try:
        success = client.home_z_axis()
        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error homing Z-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# --- Phase 2 Manual Control Endpoints ---

@app.route('/api/move_x', methods=['POST'])
def move_x():
    """Move X-axis to specified position."""
    try:
        data = request.get_json()
        position = float(data.get('position', 0))
        speed = float(data.get('speed', 30))

        # Input validation
        if not (0 <= position <= 1000):  # Reasonable range for X-axis
            return jsonify({"success": False, "message": "Position must be between 0 and 1000 mm"}), 400
        if not (1 <= speed <= 100):  # Reasonable speed range
            return jsonify({"success": False, "message": "Speed must be between 1 and 100 mm/s"}), 400

        success = client.move_x_axis(position=position, speed=speed, mode='absolute')
        return jsonify({"success": success})
    except (ValueError, TypeError) as e:
        return jsonify({"success": False, "message": "Invalid position or speed value"}), 400
    except Exception as e:
        logging.error(f"Error moving X-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/move_z', methods=['POST'])
def move_z():
    """Move Z-axis to specified position (with offset applied)."""
    try:
        data = request.get_json()
        position = float(data.get('position', 0))
        speed = float(data.get('speed', 5))

        # Input validation
        if not (-100 <= position <= 100):  # Reasonable range for Z-axis
            return jsonify({"success": False, "message": "Position must be between -100 and 100 mm"}), 400
        if not (0.1 <= speed <= 20):  # Reasonable speed range for Z-axis
            return jsonify({"success": False, "message": "Speed must be between 0.1 and 20 mm/s"}), 400

        # Apply Z-offset to the requested position
        actual_position = position + z_offset

        success = client.move_z_axis(position=actual_position, speed=speed, mode='absolute')
        return jsonify({"success": success})
    except (ValueError, TypeError) as e:
        return jsonify({"success": False, "message": "Invalid position or speed value"}), 400
    except Exception as e:
        logging.error(f"Error moving Z-axis: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/set_z_offset', methods=['POST'])
def set_z_offset():
    """Set the Z-offset (current Z position becomes the new zero)."""
    try:
        data = request.get_json()
        offset = float(data.get('offset', 0))

        global z_offset
        z_offset = offset

        return jsonify({"success": True, "z_offset": z_offset})
    except (ValueError, TypeError) as e:
        return jsonify({"success": False, "message": "Invalid offset value"}), 400
    except Exception as e:
        logging.error(f"Error setting Z-offset: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/gripper', methods=['POST'])
def control_gripper():
    """Control gripper open/close."""
    try:
        data = request.get_json()
        state = data.get('state', '').lower()

        if state not in ['open', 'close']:
            return jsonify({"success": False, "message": "State must be 'open' or 'close'"}), 400

        if state == 'open':
            success = client.open_gripper()
        else:
            success = client.close_gripper()

        return jsonify({"success": success})
    except Exception as e:
        logging.error(f"Error controlling gripper: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

# --- Phase 3 Print Job Management Endpoints ---

@app.route('/api/upload_geometry/<int:drum_id>', methods=['POST'])
def upload_geometry(drum_id):
    """Upload geometry file for a specific drum."""
    try:
        # Validate drum_id
        if drum_id not in [0, 1, 2]:
            return jsonify({"success": False, "message": "Drum ID must be 0, 1, or 2"}), 400

        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "No file uploaded"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "No file selected"}), 400

        # Sanitize filename and validate file extension
        filename = secure_filename(file.filename)
        if not filename:
            return jsonify({"success": False, "message": "Invalid filename"}), 400

        # Validate file extension - only allow .cli and .png files
        allowed_extensions = {'.cli', '.png'}
        file_ext = '.' + filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''
        if file_ext not in allowed_extensions:
            return jsonify({"success": False, "message": "Only .cli and .png files are allowed"}), 400

        # Read file data
        geometry_data = file.read()

        # Validate file size (reasonable limit)
        if len(geometry_data) > 50 * 1024 * 1024:  # 50MB limit
            return jsonify({"success": False, "message": "File too large (max 50MB)"}), 400

        # Upload to recoater
        success = client.set_drum_geometry(drum_id, geometry_data)

        if success:
            return jsonify({"success": True, "message": f"Geometry uploaded to drum {drum_id} ({filename})"})
        else:
            return jsonify({"success": False, "message": "Failed to upload geometry to recoater"}), 500

    except Exception as e:
        logging.error(f"Error uploading geometry to drum {drum_id}: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/print/parameters', methods=['POST'])
def set_print_parameters():
    """Set print parameters."""
    try:
        data = request.get_json()

        # Validate required parameters
        required_params = ['layer_start', 'layer_end', 'filling_drum_id', 'patterning_speed', 'travel_speed']
        for param in required_params:
            if param not in data:
                return jsonify({"success": False, "message": f"Missing required parameter: {param}"}), 400

        # Validate parameter values
        layer_start = int(data['layer_start'])
        layer_end = int(data['layer_end'])
        filling_drum_id = int(data['filling_drum_id'])
        patterning_speed = float(data['patterning_speed'])
        travel_speed = float(data['travel_speed'])
        powder_saving = data.get('powder_saving', False)

        # Optional parameters with defaults from config or reasonable defaults
        x_offset = float(data.get('x_offset', 0.0))
        z_offset = float(data.get('z_offset', 0.0))
        layer_thickness = float(data.get('layer_thickness', 0.08))  # 80 microns default
        collectors_delay = int(data.get('collectors_delay', 0))

        # Basic validation
        if layer_start < 1 or layer_end < layer_start:
            return jsonify({"success": False, "message": "Invalid layer range"}), 400
        # Fix: Allow -1 for "no filling powder"
        if filling_drum_id not in [-1, 0, 1, 2]:
            return jsonify({"success": False, "message": "Filling drum ID must be -1 (no filling), 0, 1, or 2"}), 400
        if patterning_speed <= 0 or travel_speed <= 0:
            return jsonify({"success": False, "message": "Speeds must be positive"}), 400
        if layer_thickness <= 0:
            return jsonify({"success": False, "message": "Layer thickness must be positive"}), 400

        # Prepare parameters for recoater client (use API field names)
        print_params = {
            'layer_start': layer_start,
            'layer_end': layer_end,
            'filling_id': filling_drum_id,  # API uses 'filling_id', not 'filling_drum_id'
            'patterning_speed': patterning_speed,
            'travel_speed': travel_speed,
            'powder_saving': powder_saving,
            'x_offset': x_offset,
            'z_offset': z_offset,
            'layer_thickness': layer_thickness,
            'collectors_delay': collectors_delay
        }

        # Set parameters on recoater
        success = client.set_print_parameters(**print_params)

        if success:
            return jsonify({"success": True, "message": "Print parameters set successfully"})
        else:
            return jsonify({"success": False, "message": "Failed to set print parameters"}), 500

    except (ValueError, TypeError) as e:
        return jsonify({"success": False, "message": "Invalid parameter values"}), 400
    except Exception as e:
        logging.error(f"Error setting print parameters: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/print/start', methods=['POST'])
def start_print():
    """Start a print job."""
    try:
        # Check system state before starting print
        try:
            state_info = client.get_state()
            current_state = state_info.get('state', 'unknown')
        except Exception as e:
            logging.error(f"Error getting system state: {e}")
            return jsonify({"success": False, "message": "Cannot check system state"}), 500

        # Only allow print start if system is ready
        if current_state != 'ready':
            return jsonify({
                "success": False,
                "message": f"Cannot start print: system state is '{current_state}', must be 'ready'"
            }), 409  # 409 Conflict

        success = client.start_print_job()

        if success:
            return jsonify({"success": True, "message": "Print job started"})
        else:
            return jsonify({"success": False, "message": "Failed to start print job"}), 500

    except Exception as e:
        logging.error(f"Error starting print job: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/print/cancel', methods=['POST'])
def cancel_print():
    """Cancel the current print job."""
    try:
        success = client.cancel_print_job()

        if success:
            return jsonify({"success": True, "message": "Print job cancelled"})
        else:
            return jsonify({"success": False, "message": "Failed to cancel print job"}), 500

    except Exception as e:
        logging.error(f"Error cancelling print job: {e}")
        return jsonify({"success": False, "message": str(e)}), 500

if __name__ == '__main__':
    # Use host='0.0.0.0' to make it accessible on the network
    app.run(host='0.0.0.0', port=5001, debug=True)
